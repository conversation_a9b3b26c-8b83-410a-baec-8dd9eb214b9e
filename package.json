{"name": "ALP_DMS", "version": "0.0.1", "private": true, "scripts": {"android": "set NO_FLIPPER=1 && react-native run-android", "ios": "export NO_FLIPPER=1 && react-native run-ios", "lint": "eslint .", "start": "set NO_FLIPPER=1 && react-native start", "test": "jest"}, "dependencies": {"@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@reduxjs/toolkit": "^2.8.2", "nativewind": "^4.0.36", "react": "18.2.0", "react-native": "0.73.9", "react-native-safe-area-context": "^4.10.0", "react-native-screens": "3.29.0", "react-redux": "^9.2.0", "tailwindcss": "^3.4.10"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}