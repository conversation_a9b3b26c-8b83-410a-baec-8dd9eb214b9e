import React from 'react';
import SignInScreen from '../screens/auth/signInScreen';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {NavigationContainer} from '@react-navigation/native';

const Stack = createNativeStackNavigator();

export default function AppNavigator() {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="SignIn">
        <Stack.Screen name="SignIn" component={SignInScreen} />
        {/* <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} /> <Stack.Screen name="Cart" component={CartScreen} /> <Stack.Screen name="Orders" component={OrdersScreen} /> <Stack.Screen name="Profile" component={ProfileScreen} /> <Stack.Screen name="Addresses" component={AddressesScreen} /> */}
      </Stack.Navigator>
    </NavigationContainer>
  );
}
