import {createSlice, PayloadAction} from '@reduxjs/toolkit';

interface AuthState {
  user: string | null;
  token: string | null;
}

const initialState: AuthState = {
  user: null,
  token: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    signIn: (state, action: PayloadAction<{user: string; token: string}>) => {
      state.user = action.payload.user;
      state.token = action.payload.token;
    },
    signOut: state => {
      state.user = null;
      state.token = null;
    },
  },
});

export const {signIn, signOut} = authSlice.actions;
export default authSlice.reducer;
